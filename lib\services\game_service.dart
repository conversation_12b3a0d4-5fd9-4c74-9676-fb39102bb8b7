import 'package:hive/hive.dart';
import '../hive/poker_table.dart';

/// Service class for managing poker games/tables data from Hive database
class GameService {
  /// Box name for poker tables in Hive
  static const String _boxName = 'poker_tables';

  /// Retrieves all poker games from the Hive database
  /// Returns a list of maps formatted for UI consumption
  static Future<List<Map<String, String>>> getGamesFromHive() async {
    try {
      // Get the poker tables box
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      
      // Get all poker tables from the box
      final List<PokerTable> tables = box.values.toList();
      
      // Convert PokerTable objects to UI-friendly format
      final List<Map<String, String>> games = tables.map((table) => _convertTableToGameMap(table)).toList();
      
      return games;
    } catch (e) {
      // Return empty list if something goes wrong
      return [];
    }
  }

  /// Retrieves all poker games synchronously from the Hive database
  /// Returns a list of maps formatted for UI consumption
  static List<Map<String, String>> getGamesFromHiveSync() {
    try {
      // Get the poker tables box
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      
      // Get all poker tables from the box
      final List<PokerTable> tables = box.values.toList();
      
      // Convert PokerTable objects to UI-friendly format
      final List<Map<String, String>> games = tables.map((table) => _convertTableToGameMap(table)).toList();
      
      return games;
    } catch (e) {
      // Return empty list if something goes wrong
      return [];
    }
  }

  /// Retrieves active poker games (tables with players) from the Hive database
  static List<Map<String, String>> getActiveGamesFromHive() {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      final List<PokerTable> tables = box.values.toList();

      // Filter for tables that have active players
      final List<PokerTable> activeTables = tables.where((table) {
        return table.seatMap.values.any((playerId) => playerId != null);
      }).toList();

      return activeTables.map((table) => _convertTableToGameMap(table)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Retrieves tournament games from the Hive database
  static List<Map<String, String>> getTournamentGamesFromHive() {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      final List<PokerTable> tables = box.values.toList();

      // Filter for tournament tables
      final List<PokerTable> tournamentTables = tables.where((table) => table.isTournament).toList();

      return tournamentTables.map((table) => _convertTableToGameMap(table)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Retrieves cash games from the Hive database
  static List<Map<String, String>> getCashGamesFromHive() {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      final List<PokerTable> tables = box.values.toList();

      // Filter for cash game tables
      final List<PokerTable> cashTables = tables.where((table) => !table.isTournament).toList();

      return cashTables.map((table) => _convertTableToGameMap(table)).toList();
    } catch (e) {
      return [];
    }
  }

  /// Converts a PokerTable object to a Map format expected by the UI components
  static Map<String, String> _convertTableToGameMap(PokerTable table) {
    // Calculate number of active players
    final int activePlayers = table.seatMap.values.where((playerId) => playerId != null).length;
    
    // Format blinds
    final String blinds = '\$${table.smallBlind}/${table.bigBlind}';
    
    // Format buy-in based on table type
    String buyIn;
    if (table.isTournament) {
      buyIn = table.prizePool != null ? '\$${table.prizePool!.toStringAsFixed(0)}' : 'N/A';
    } else {
      buyIn = '\$${table.minBuyIn.toStringAsFixed(0)} - \$${table.maxBuyIn.toStringAsFixed(0)}';
    }
    
    // Calculate wait time (placeholder - could be enhanced with actual logic)
    final String waitTime = activePlayers >= table.seatCount ? '5:00' : '0:00';
    
    // Calculate hours (placeholder - could be enhanced with actual start time logic)
    String hours = '0.0';
    if (table.isTournament && table.tournamentStartTime != null) {
      final Duration elapsed = DateTime.now().difference(table.tournamentStartTime!);
      hours = (elapsed.inMinutes / 60.0).toStringAsFixed(1);
    }
    
    // Determine game type
    String gameType = 'Texas Hold\'em'; // Default, could be enhanced with more game types
    if (table.isTournament) {
      gameType = 'Tournament';
    }
    
    // Generate a display name for the table
    String tableName = table.tableId;
    if (table.isTournament) {
      tableName = 'Tournament ${table.tableId}';
    } else {
      tableName = 'Cash Game ${table.tableId}';
    }
    
    return {
      'type': gameType,
      'name': tableName,
      'blinds': blinds,
      'buyIn': buyIn,
      'players': '$activePlayers/${table.seatCount}',
      'wait': waitTime,
      'hours': hours,
      'tableId': table.tableId, // Include tableId for navigation purposes
    };
  }

  /// Adds a new poker table to the Hive database
  static Future<void> addGameToHive(PokerTable table) async {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      await box.add(table);
    } catch (e) {
      rethrow;
    }
  }

  /// Updates an existing poker table in the Hive database
  static Future<void> updateGameInHive(String tableId, PokerTable updatedTable) async {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);

      // Find the table by tableId
      final List<PokerTable> tables = box.values.toList();
      final int index = tables.indexWhere((table) => table.tableId == tableId);

      if (index != -1) {
        await box.putAt(index, updatedTable);
      } else {
        throw Exception('Table with ID $tableId not found');
      }
    } catch (e) {
      // Log error and rethrow
      rethrow;
    }
  }

  /// Removes a poker table from the Hive database
  static Future<void> removeGameFromHive(String tableId) async {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      
      // Find and remove the table by tableId
      final List<PokerTable> tables = box.values.toList();
      for (int i = 0; i < tables.length; i++) {
        if (tables[i].tableId == tableId) {
          await box.deleteAt(i);
          break;
        }
      }
    } catch (e) {
      rethrow;
    }
  }

  /// Gets the count of total games in the database
  static int getGameCount() {
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>(_boxName);
      return box.length;
    } catch (e) {
      return 0;
    }
  }

  /// Checks if the Hive box is open and ready for operations
  static bool isHiveReady() {
    try {
      return Hive.isBoxOpen(_boxName);
    } catch (e) {
      return false;
    }
  }
}
