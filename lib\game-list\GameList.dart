import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:four_leaf_poker/game-list/game_lobby_layout.dart';

import '../table/header/custom_menu.dart';


/// Offsets to apply on mobile: first = circle offset, second = tab offset (unused here)
class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

class GameListPage extends StatefulWidget {
  const GameListPage({super.key});

  @override
  State<GameListPage> createState() => _GameListPageState();
}

class _GameListPageState extends State<GameListPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF383838),
      body: SizedBox.expand(
    child:  Stack(
        children: [

          // Table graphic + seats
          Positioned.fill(
            child: Container(

              decoration: const BoxDecoration(
                color: Color.fromARGB(255, 7, 32, 30),
                image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                    opacity: .2),
              ),
            ),
          ),
          //   child: WinningsDisplay())
         Align(
          alignment: Alignment.topCenter,
            child: CustomMenu(onMenuPressed: ()=>{}, onSettingsPressed: ()=>{}, showCountdown: false),
         ),
         Center(
          child: GameLobbyLayout()
         ),          

        ],
      ),)
    );
  }

 

}
