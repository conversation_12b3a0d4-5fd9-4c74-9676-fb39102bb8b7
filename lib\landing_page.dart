import 'package:flutter/material.dart';
import 'package:four_leaf_poker/menu_bar.dart';

class LandingPage extends StatelessWidget {
  LandingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      backgroundColor: Colors.grey.shade200,
      body: SizedBox.expand(
        child: Stack(
          children: [
            // Dark background
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  color: Color.fromARGB(255, 7, 32, 30),
                  image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                    opacity: .2
                  ),
                ),
              ),
            ),

            if(!isMobile)
            Positioned.fill(
              child: Center(
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/casino'),
                    child: Image.asset(
                      'assets/images/flag1.png',
                      width: 1300 * MediaQuery.of(context).size.width / 1920,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
            if(isMobile)
            Positioned.fill(
              child: Center(
                child: MouseRegion(
                  cursor: SystemMouseCursors.click,
                  child: GestureDetector(
                    onTap: () => Navigator.pushNamed(context, '/casino'),
                    child: Image.asset(
                      'assets/images/VertCharacters.png',
                      width: MediaQuery.of(context).size.width,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
            // Centered table with seats
            if(!isMobile)
            MyMenuBar(
              onRegister: () {},
              onLogin: () {},
            ),
          ],
        ),
      ),
    );
  }
}
