import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:four_leaf_poker/api.dart';
import 'package:four_leaf_poker/table/extras/winnings_display.dart';
import 'package:four_leaf_poker/table/header/custom_menu.dart';
import 'package:universal_html/html.dart' as html;
import 'table_painter.dart'; // same custom painter as in your main code

/// Offsets to apply on mobile: first = circle offset, second = tab offset (unused here)
class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

class CreateTablePage extends StatefulWidget {
  const CreateTablePage({Key? key}) : super(key: key);

  @override
  State<CreateTablePage> createState() => _CreateTablePageState();
}

class _CreateTablePageState extends State<CreateTablePage> {
  // The seat-count
  final int seatCount = 9;
  List<bool> seatActive = [];
  final List<String> _dealerStartOptions =
      ['Random'] + List.generate(9, (i) => (i + 1).toString());
  String _selectedDealerStart = 'Random';

  // Toggle for whether players can join after start
  bool _canJoinAfterStart = false;

  // Toggle for Rebuys
  bool _rebuysActive = false;
  // Possible rebuy counts (including "Unlimited")
  final List<String> _rebuyCounts = ["1", "2", "3", "4", "5", "Unlimited"];
  // Default selection
  String _selectedRebuyCount = "1";

  final List<String> _turnClockOptions = [
    'Unlimited',
    '1 minute',
    '30 seconds',
    '20 seconds',
    '10 seconds',
  ];
  String _selectedTurnClock = '20 seconds';

  // Ellipse seat offsets for mobile
  final Map<int, SeatOffset> _mobileSeatOffsetOverrides = {
    0: const SeatOffset(
      Offset(-5, 40),
      Offset(-25, -50),
    ),
    1: const SeatOffset(
      Offset(-40, 0),
      Offset(-25, -50),
    ),
    2: const SeatOffset(
      Offset(0, -20),
      Offset(5, -40),
    ),
    3: const SeatOffset(
      Offset(0, -30),
      Offset(-10, -40),
    ),
    4: const SeatOffset(
      Offset(-10, -40),
      Offset(-10, -30),
    ),
    5: const SeatOffset(
      Offset(10, -40),
      Offset(10, -30),
    ),
    6: const SeatOffset(
      Offset(0, -30),
      Offset(0, -40),
    ),
    7: const SeatOffset(
      Offset(0, -20),
      Offset(-15, -40),
    ),
    8: const SeatOffset(
      Offset(40, 0),
      Offset(15, -50),
    ),
  };

  /// how wide our seat‑circle should be
  double get _seatCircleRx {
    final w = MediaQuery.of(context).size.width;
    // if portrait (e.g. 390×844), make it ~90% of width/2
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (w * 0.85) / 2;
    }
    // otherwise fall back to your existing value
    return 370.0;
  }

  /// how tall our seat‑circle should be
  double get _seatCircleRy {
    final h = MediaQuery.of(context).size.height;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (h * 0.3); // tweak this multiplier until it “feels” right
    }
    return 270.0;
  }

  int? _turnClockToMs(String selected) {
    switch (selected) {
      case '1 minute':
        return 60 * 1000;
      case '30 seconds':
        return 30 * 1000;
      case '20 seconds':
        return 20 * 1000;
      case '10 seconds':
        return 10 * 1000;
      case 'Unlimited':
      default:
        return null; // use null to represent "no timer"
    }
  }

  // Blinds, stacks, seats
  final List<String> _blindLevels = ['1/2', '10/20', '100/200'];
  final List<String> _startingStacks = [
    '1,000',
    '2,000',
    '3,000',
    '4,000',
    '5,000',
    'Random',
  ];
  final List<String> _seatPositions =
      List.generate(9, (i) => (i + 1).toString());

  String _selectedBlindLevel = '100/200';
  String _selectedStartingStack = '1,000';
  String _selectedSeatPosition = '1';
  bool _isLoading = false;

  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _handleCreateTable() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _isLoading = true);

    // Figure out which seat to use for the dealer button:
    final int dealerStartSeat = _selectedDealerStart == 'Random'
        // Random: pick 1…seatCount
        ? math.Random().nextInt(seatCount) + 1
        // Otherwise parse the selected string
        : int.parse(_selectedDealerStart);

    // 1) Parse the blind-level string, e.g. "1/2" => sb=1, bb=2
    final blindParts = _selectedBlindLevel.split('/');
    int sb = 0;
    int bb = 0;
    if (blindParts.length == 2) {
      // Remove commas if any (e.g. "1,000/2,000") and parse
      sb = int.tryParse(blindParts[0].replaceAll(',', '')) ?? 1;
      bb = int.tryParse(blindParts[1].replaceAll(',', '')) ?? 2;
    }

    debugPrint('=== _handleCreateTable Debug Info ===');
    debugPrint("Name: '${_nameController.text.trim()}', "
        "Blind: $_selectedBlindLevel, "
        "Stack: $_selectedStartingStack, "
        "Seat: $_selectedSeatPosition, "
        "seatCount=$seatCount, "
        "canJoinAfterStart=$_canJoinAfterStart, "
        "rebuysActive=$_rebuysActive, "
        "rebuyCount=$_selectedRebuyCount,"
        "turnClock=$_selectedTurnClock");

    try {
      final turnDurationMs = _turnClockToMs(_selectedTurnClock);
      // 1) Create table
      final result = await Api.openNewTable(
        name: _nameController.text.trim(),
        blindLevel: _selectedBlindLevel,
        startingStack: _selectedStartingStack,
        seatCount: seatCount,
        canJoinAfterStart: _canJoinAfterStart,
        rebuysActive: _rebuysActive,
        rebuyCount: _selectedRebuyCount,
        turnDurationMs: turnDurationMs,
        sbCost: sb,
        bbCost: bb,
        dealerStart: dealerStartSeat,
      );
      final roomId = result['tableId'];

      // 2) Immediately join that table
      final seatPos = int.parse(_selectedSeatPosition);
      final confirmJoin = await Api.joinTable(
        tableId: roomId!,
        name: _nameController.text.trim(),
        seatPosition: seatPos,
      );

      // 3) Store data in localStorage
      html.window.localStorage['tableId'] = roomId;
      html.window.localStorage['seatPosition'] = _selectedSeatPosition;
      html.window.localStorage['playerId'] = confirmJoin['playerId'];
      html.window.localStorage['isCreator'] = 'true';

      // 4) Navigate to the game page
      Navigator.pushNamed(
        context,
        '/game/$roomId',
        arguments: {
          'tableId': roomId,
          'playerId': confirmJoin['playerId'],
          'seatPosition': seatPos,
          'playerName': _nameController.text.trim(),
        },
      );
    } catch (e, stackTrace) {
      debugPrint("Failed to create table: $e\n$stackTrace");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error creating table: $e")),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _handleCreateGame() async {
    await _handleCreateTable();
  }

  // Build seats in a circle (decorative only):

  @override
  Widget build(BuildContext context) {
    final bool isMobile = MediaQuery.of(context).size.width < 600;

    return Scaffold(
      backgroundColor: const Color(0xFF383838),
      body: Stack(
        children: [
          // Table graphic + seats
          Positioned.fill(
            child: Container(

              decoration: const BoxDecoration(
                color: Color.fromARGB(255, 7, 32, 30),
                image: DecorationImage(
                    image: AssetImage('assets/images/bg.png'),
                    fit: BoxFit.cover,
                    opacity: .2),
              ),
            ),
          ),Transform.scale(
          scale: isMobile ? .8 : 1.4,
          child:           Align(
            alignment: Alignment.center,
            child: SizedBox(
              width: 700,
              height: 500,
              child: LayoutBuilder(
                builder: (ctx, constraints) {
                final fullW = constraints.maxWidth;
                final fullH = constraints.maxHeight;
                final isPortrait = fullH > fullW;
                  return Stack(
                    clipBehavior: Clip.none,
                    alignment: Alignment.center,
                    children: [
                       ..._buildPlayerSeats(parentWidth: fullW, parentHeight: fullH, isMobile: isMobile), 
                      Image.asset(
                        'assets/images/table.png',
                        width: 600,
                        height: 500,
                        fit: BoxFit.contain,
                        opacity: const AlwaysStoppedAnimation(0.2),
                      ),
                                          
                    ],
                  );
                },
              ),
            ),
          ),
          ),
        if(!isMobile) 
         Positioned(
          top: 0,
          width: MediaQuery.of(context).size.width,
          child: CustomMenu(showCountdown: false, countDown: '')),
          Positioned(
              bottom: 0,
              right: MediaQuery.of(context).size.width / 2 - 75,
              child: Image.asset(
                'assets/images/left-lady.png',
              )),
          Positioned(
              bottom: 0,
              left: MediaQuery.of(context).size.width / 2,
              child: Image.asset(
                'assets/images/right-lady.png',
              )),
          // Form dialog
          Center(
            child: Container(
              width: 370,
              padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 18),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color.fromARGB(255, 0, 88, 91),
                    Color.fromARGB(255, 2, 34, 37)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),

                // color: const Color(0xFF06606B),
                borderRadius: BorderRadius.circular(26),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 32,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Top bar
                  Padding(
                    padding: const EdgeInsets.only(
                        top: 16, left: 8, right: 8, bottom: 8),
                    child: Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.arrow_back,
                              color: Colors.white, size: 28),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                        const Expanded(child: SizedBox()),
                        Stack(
                          children: [
                            Text(
                              "GAME RULES",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 4
                                  ..color = Colors.black,
                              ),
                            ),
                            const Text(
                              "GAME RULES",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        const Expanded(child: SizedBox()),
                      ],
                    ),
                  ),
                  // Name field styled as in image
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                    child: Container(
                      height: 56,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.10),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.18),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: TextFormField(
                          textAlign: TextAlign.center,
                          controller: _nameController,
                          cursorColor: const Color(0xFF4FC3F7),
                          maxLength: 16,
                          buildCounter: (ctx,
                                  {required currentLength,
                                  required isFocused,
                                  maxLength}) =>
                              null,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                          ),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: 'Your table name',
                            hintStyle: TextStyle(
                              color: Color(0xFF7CA1A7),
                              fontWeight: FontWeight.w900,
                              fontSize: 22,
                            ),
                            counterText: '',
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Name is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                  ),
                  // Main card for form fields
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                    child: Container(
                      decoration: BoxDecoration(
                        // color: const Color(0xFF08777F),
                        borderRadius: BorderRadius.circular(22),
                        border: Border.all(
                            color: Colors.black.withOpacity(0.18), width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.18),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(
                          vertical: 18, horizontal: 8),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            // Each row: label + field
                            _formRow(
                              label: 'Blinds:',
                              child: _pillDropdown(
                                value: _selectedBlindLevel,
                                items: _blindLevels,
                                onChanged: (v) =>
                                    setState(() => _selectedBlindLevel = v!),
                              ),
                            ),
                            _formRow(
                              label: 'Starting Stack:',
                              child: _pillDropdown(
                                value: _selectedStartingStack,
                                items: _startingStacks,
                                onChanged: (v) =>
                                    setState(() => _selectedStartingStack = v!),
                              ),
                            ),
                            _formRow(
                              label: 'Selected Seat:',
                              child: _pillDropdown(
                                value: _selectedSeatPosition,
                                items: _seatPositions,
                                onChanged: (v) =>
                                    setState(() => _selectedSeatPosition = v!),
                              ),
                            ),
                            _formRow(
                              label: 'Dealer Start:',
                              child: _pillDropdown(
                                value: _selectedDealerStart,
                                items: _dealerStartOptions,
                                onChanged: (v) =>
                                    setState(() => _selectedDealerStart = v!),
                              ),
                            ),
                            _formRow(
                              label: 'Joinable after start:',
                              child: _customSwitch(
                                  _canJoinAfterStart,
                                  (v) =>
                                      setState(() => _canJoinAfterStart = v)),
                            ),
                            _formRow(
                              label: 'Turn Clock:',
                              child: _pillDropdown(
                                value: _selectedTurnClock,
                                items: _turnClockOptions,
                                onChanged: (v) =>
                                    setState(() => _selectedTurnClock = v!),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 20),
                  // Create Table button
                  Align(
                    alignment: Alignment.bottomCenter,
                    child: GestureDetector(
                      onTap: _isLoading ? null : _handleCreateGame,
                      child: Image.asset(
                        'assets/images/CreateGame2.png',
                        width: 320,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Align(
          //   alignment: Alignment.bottomRight,
          //   child: WinningsDisplay())
        ],
      ),
    );
  }

  // Helper for form row
  Widget _formRow({required String label, required Widget child}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Stack(
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w900,
                  foreground: Paint()
                    ..style = PaintingStyle.stroke
                    ..strokeWidth = 3
                    ..color = Colors.black,
                ),
              ),
              Text(
                label,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          child,
        ],
      ),
    );
  }

  // Helper for pill dropdown
  Widget _pillDropdown({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      width: 160,
      height: 48,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color.fromARGB(255, 0, 88, 91),
            Color.fromARGB(255, 2, 34, 37)
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          dropdownColor: const Color(0xFF08777F),
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w900,
            fontSize: 14,
            shadows: [
              Shadow(blurRadius: 2, color: Colors.black, offset: Offset(1, 1))
            ],
          ),
          onChanged: onChanged,
          items: items
              .map((item) => DropdownMenuItem<String>(
                    value: item,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      child: Stack(
                        children: [
                          Text(
                            item,
                            style: TextStyle(
                              fontWeight: FontWeight.w900,
                              fontSize: 20,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 3
                                ..color = Colors.black,
                            ),
                          ),
                          Text(
                            item,
                            style: const TextStyle(
                              fontWeight: FontWeight.w900,
                              fontSize: 20,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }

  // Helper for custom switch
  Widget _customSwitch(bool value, ValueChanged<bool> onChanged) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 180),
        width: 54,
        height: 24,
        padding: const EdgeInsets.all(1),
        decoration: BoxDecoration(
          color: const Color.fromARGB(255, 0, 0, 0),
          borderRadius: BorderRadius.circular(20),
          // border: Border.all(color: Colors.black, width: 2),
          // // boxShadow: [
          // //   BoxShadow(
          // //     color: Colors.black.withOpacity(0.25),
          // //     blurRadius: 4,
          // //     offset: const Offset(0, 2),
          // //   ),
          // ],
        ),
        child: Align(
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 22,
            height: 22,
            decoration: BoxDecoration(
              color: value ? const Color(0xFF0EDB8B) : const Color(0xFF1B2B2B),
              borderRadius: BorderRadius.circular(16),
              // border: Border.all(color: Colors.black, width: 1),
            ),
            child: Container(
              width: 22,
              height: 22,
              //margin: const EdgeInsets.all(3),
              decoration: BoxDecoration(
                color:
                    value ? const Color(0xFF0EDB8B) : const Color(0xFF3A3A3A),
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildPlayerSeats({
    required double parentWidth,
    required double parentHeight,
    required bool isMobile,
    final empty = true,
  }) {
    final seatCount = 9;
    final mySeat =  0;
    final seatWidgets = <Widget>[];

    // Center of the table
    final cx = parentWidth / 2;
    final cy = parentHeight / 2;

    // Radii for ellipse
    final rx = _seatCircleRx;
    final ry = _seatCircleRy;

    for (int offset = 0; offset < seatCount; offset++) {
      // "Real" seat index rotated so mySeat is at offset=0
      final realSeat = (mySeat + offset) % seatCount;

      // Compute angle and base position
      final angle = (math.pi / 2) + offset * (2 * math.pi / seatCount);
      final baseX = cx + rx * math.cos(angle);
      final baseY = cy + ry * math.sin(angle);

      // Initialize both circle and tab positions to base
      var sxCircle = baseX;
      var syCircle = baseY;
      var sxTab = baseX;
      var syTab = baseY;

      // Apply mobile-specific overrides if provided
      if (isMobile && _mobileSeatPositionOffsets.containsKey(offset)) {
        final o = _mobileSeatPositionOffsets[offset]!;
        sxCircle += o.circle.dx;
        syCircle += o.circle.dy;
        sxTab += o.tab.dx;
        syTab += o.tab.dy;
      }

     
        // Inactive seat: draw the seat circle with number
        seatWidgets.add(
          Positioned(
            left: sxCircle - 100,
            top: syCircle - 65,
            child: Transform.rotate(
                angle: ![4, 5].contains(offset) ? angle + math.pi / 2 : 0,
                //angle:0,
                child: Column(children: [
                  //   Text('myseat: $mySeat  realSeat: $realSeat  offset: $offset'),
                  Image.asset(
                    'assets/images/chair.png',
                    opacity: const AlwaysStoppedAnimation(0.05) ,
                    width: 200,
                    height: 154,
                    fit: BoxFit.scaleDown,
                  )
                ])),
          ),
        );
      
    }

    return seatWidgets;
  }

  Widget _buildBetStack(int betAmt) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Image.asset(
          "assets/images/chips.png",
          width: 32,
          height: 32,
          fit: BoxFit.cover,
        ),
        const SizedBox(width: 4),
        Text(
          "\$$betAmt",
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        ),
      ],
    );
  }
  /// Offsets to apply when on mobile, *keyed* by localIndex (0 = you)
  final Map<int, SeatOffset> _mobileSeatPositionOffsets = {
    //0: const SeatOffset( Offset(-7.5,  40), Offset(-25, -50) ),
    1: const SeatOffset(Offset(-40, 0), Offset(-25, -50)),
    2: const SeatOffset(Offset(0, -20), Offset(5, -40)),
    3: const SeatOffset(Offset(0, -30), Offset(-10, -40)),
    4: const SeatOffset(Offset(-10, -40), Offset(-10, -30)),
    5: const SeatOffset(Offset(10, -40), Offset(10, -30)),
    6: const SeatOffset(Offset(0, -30), Offset(0, -40)),
    7: const SeatOffset(Offset(0, -20), Offset(-15, -40)),
    8: const SeatOffset(Offset(40, 0), Offset(15, -50)),
  };


}
