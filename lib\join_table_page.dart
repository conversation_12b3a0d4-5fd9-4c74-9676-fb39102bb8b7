import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:four_leaf_poker/api.dart';
import 'package:stacked/stacked_annotations.dart';
import 'package:universal_html/html.dart' as html;
import 'table_painter.dart'; // same custom painter

/// Offsets to apply on mobile: first = circle, second = tab (tab unused here)
class SeatOffset {
  final Offset circle;
  final Offset tab;
  const SeatOffset(this.circle, this.tab);
}

class JoinTablePage extends StatefulWidget {
  final String roomId;

  const JoinTablePage({
    Key? key,
    @PathParam('roomId') required this.roomId,
  }) : super(key: key);

  @override
  State<JoinTablePage> createState() => _JoinTablePageState();
}

class _JoinTablePageState extends State<JoinTablePage> {
  final int seatCount = 9;

  // Store which seats are "filled" (0-based seat indexes).
  List<int> _filledSeats = [];

  // Our user’s seat selection (also 0-based).
  int _selectedSeatPosition = -1;

  // Reference to the table data from the server.
  Map<String, dynamic>? _tableData;
  bool _isLoading = false; // Whether we’re performing the joinTable call
  bool _isFetching = true; // Whether we’re still fetching tableData
  final TextEditingController _nameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  // Mobile seat offset overrides, to shift circles on smaller screens
  final Map<int, SeatOffset> _mobileSeatOffsetOverrides = {
    0: const SeatOffset(
      Offset(-5, 40),
      Offset(-25, -50),
    ),
    1: const SeatOffset(
      Offset(-40, 0),
      Offset(-25, -50),
    ),
    2: const SeatOffset(
      Offset(0, -20),
      Offset(5, -40),
    ),
    3: const SeatOffset(
      Offset(0, -30),
      Offset(-10, -40),
    ),
    4: const SeatOffset(
      Offset(-10, -40),
      Offset(-10, -30),
    ),
    5: const SeatOffset(
      Offset(10, -40),
      Offset(10, -30),
    ),
    6: const SeatOffset(
      Offset(0, -30),
      Offset(0, -40),
    ),
    7: const SeatOffset(
      Offset(0, -20),
      Offset(-15, -40),
    ),
    8: const SeatOffset(
      Offset(40, 0),
      Offset(15, -50),
    ),
  };

  double _seatCircleRx(BuildContext context) {
    final w = MediaQuery.of(context).size.width;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (w * 0.85) / 2;
    }
    return 370.0;
  }

  double _seatCircleRy(BuildContext context) {
    final h = MediaQuery.of(context).size.height;
    if (MediaQuery.of(context).orientation == Orientation.portrait) {
      return (h * 0.30);
    }
    return 270.0;
  }

  @override
  void initState() {
    super.initState();
    // Attempt to auto-rejoin if sessionStorage indicates we're already seated.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _tryAutoRejoin();
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  /// If localStorage has tableId & playerId, and the server still lists us
  /// in that table, navigate straight to the game screen.
Future<void> _tryAutoRejoin() async {
  final tableId = html.window.localStorage['tableId'];
  final playerId = html.window.localStorage['playerId'];
  if (tableId != null && playerId != null) {
    try {
      final data = await Api.getTableData(tableId);
      final players = (data['players'] as List).cast<Map<String, dynamic>>();

      Map<String, dynamic>? you;
      try {
        you = players.firstWhere((p) => p['id'] == playerId);
      } catch (e) {
        you = null;
      }

      if (you != null) {
        // Already seated — go right back into the game.
        Navigator.pushReplacementNamed(
          context,
          '/game/$tableId',
          arguments: {
            'tableId': tableId,
            'playerId': playerId,
            'seatPosition': (you['seatNumber'] as int) + 1,
            'playerName': you['name'] as String,
          },
        );
        return;
      }
    } catch (_) {
      // ignore and fall back to showing the join form
    }
  }

  await _fetchTableData();
}

  Future<void> _fetchTableData() async {
    setState(() => _isFetching = true);
    try {
      final roomId = widget.roomId;
      if (roomId.isEmpty) {
        throw Exception('No table roomId provided to JoinTablePage.');
      }

      final tableData = await Api.getTableData(roomId);
      setState(() {
        _tableData = tableData;
        final players = tableData['players'] as List<dynamic>? ?? [];
        _filledSeats = players
            .map((p) => p['seatNumber'] as int)
            .toList();
        _isFetching = false;
      });

      final available = _getAvailableSeats();
      if (available.isNotEmpty) {
        _selectedSeatPosition = available.first;
      }
    } catch (e) {
      debugPrint('Error fetching table data: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
      setState(() => _isFetching = false);
    }
  }

  List<int> _getAvailableSeats() {
    final all = List<int>.generate(seatCount, (i) => i);
    return all
      .where((i) => !_filledSeats.contains(i))
      .toList()
      ..sort();
  }

  Future<void> _handleJoinTable() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedSeatPosition < 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select a seat')),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final roomId = widget.roomId;
      final seatPos = _selectedSeatPosition + 1; // convert 0-based to 1-based

      final result = await Api.joinTable(
        tableId: roomId,
        name: _nameController.text.trim(),
        seatPosition: seatPos,
      );

      // Mark localStorage so we know we’re not the table creator
      html.window.localStorage['isCreator'] = 'false';
      html.window.localStorage['tableId']  = roomId;
      html.window.localStorage['playerId'] = result['playerId'];

      // Then navigate to the game page
      Navigator.pushReplacementNamed(
        context,
        '/game/$roomId',
        arguments: {
          'tableId': roomId,
          'playerId': result['playerId'],
          'seatPosition': seatPos,
          'playerName': _nameController.text.trim(),
        },
      );
    } catch (error) {
      debugPrint('Failed to join table: $error');
      final msg = error.toString();

      if (msg.contains('409') || msg.contains('Seat already taken')) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('This seat is already taken, please pick again'),
          ),
        );
        await _fetchTableData();
      } else if (msg.contains('table has already started')) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cannot join: game has already started.'),
          ),
        );
        await _fetchTableData();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error joining table: $error')),
        );
        await _fetchTableData();
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  // Draw seat placeholders around the table:
  List<Widget> _buildSeats(BoxConstraints constraints) {
    final w = constraints.maxWidth;
    final h = constraints.maxHeight;
    final cx = w / 2;
    final cy = h / 2;
    final rx = _seatCircleRx(context);
    final ry = _seatCircleRy(context);
    final seats = <Widget>[];

    for (int i = 0; i < seatCount; i++) {
      final angle = (math.pi / 2) + i * (2 * math.pi / seatCount);
      var sx = cx + rx * math.cos(angle);
      var sy = cy + ry * math.sin(angle);

      final isMobile = MediaQuery.of(context).size.width < 600;
      if (isMobile && _mobileSeatOffsetOverrides.containsKey(i)) {
        final offsets = _mobileSeatOffsetOverrides[i]!;
        sx += offsets.circle.dx;
        sy += offsets.circle.dy;
      }

      final filled = _filledSeats.contains(i);
      final selected = (i == _selectedSeatPosition);

      seats.add(Positioned(
        left: sx - 22,
        top: sy - 22,
        child: Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: filled
                ? const Color(0xFF4E4E4E)
                : const Color.fromARGB(255, 67, 68, 68),
            border: selected
                ? Border.all(
                    color: const Color.fromARGB(255, 4, 159, 206),
                    width: 3,
                  )
                : null,
            boxShadow: selected
                ? [
                    BoxShadow(
                      color: const Color.fromARGB(255, 4, 159, 206)
                          .withOpacity(0.6),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ]
                : [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      offset: const Offset(0, 3),
                      blurRadius: 6,
                    ),
                  ],
          ),
          alignment: Alignment.center,
          child: Text(
            filled ? 'Filled' : '${i + 1}',
            style: filled
                ? const TextStyle(
                    color: Color.fromARGB(255, 29, 29, 29),
                    fontSize: 12,
                  )
                : const TextStyle(color: Colors.white, fontSize: 14),
          ),
        ),
      ));
    }

    return seats;
  }

  @override
  Widget build(BuildContext context) {
    if (_isFetching) {
      return Scaffold(
        backgroundColor: Colors.grey.shade200,
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_tableData == null) {
      return Scaffold(
        backgroundColor: Colors.grey.shade200,
        body: const Center(child: Text('No table data found.')),
      );
    }

    final tableId = _tableData!['tableId'] ?? 'Unknown ID';
    final blindLevel = _tableData!['blindLevel'] ?? 'N/A';
    final startingStack = _tableData!['startingStack'] ?? 'N/A';
    final availableSeats = _getAvailableSeats();
    return Scaffold(
      backgroundColor: const Color(0xFF383838),
      body: Stack(
        children: [
          // Top label
            Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
              color: Color.fromARGB(255, 0,33,35),
              image: DecorationImage(
                image: AssetImage('assets/images/bg.png'),
                fit: BoxFit.cover,
              ),
              ),
            ),
          ),

          // Table + seats
          Align(
            alignment: Alignment.center,
            child: SizedBox(
              width: 700,
              height: 500,
              child: LayoutBuilder(
                builder: (ctx, constraints) {
                  return Stack(
                    clipBehavior: Clip.none,
                    children: [
                           Image.asset(
                              'assets/images/table.png',
                              width: 700,
                              height: 500,
                              fit: BoxFit.contain,
                            ),
                    ],
                  );
                },
              ),
            ),
          ),

          // Join form
          Center(
            child: Container(
              width: 340,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color.fromARGB(255, 0, 88, 91),
                    Color.fromARGB(255, 2, 34, 37)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),

                // color: const Color(0xFF06606B),
                borderRadius: BorderRadius.circular(26),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 32,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              
              child: availableSeats.isEmpty
                  ? const Center(
                      child: Text(
                        'Table is full.',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                        Stack(
                          children: [
                            Text(
                              "Join Table",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 4
                                  ..color = Colors.black,
                              ),
                            ),
                            const Text(
                              "Join Table",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                       
                            const SizedBox(height: 16),

                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(8),
  
                              child: Column(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start,
                                children: [
                        Stack(
                          children: [
                            Text(
                              "Blinds: $blindLevel",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 4
                                  ..color = Colors.black,
                              ),
                            ),
                             Text(
                              "Blinds: $blindLevel",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                        Stack(
                          children: [
                            Text(
                              "Starting Stack: $startingStack",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                foreground: Paint()
                                  ..style = PaintingStyle.stroke
                                  ..strokeWidth = 4
                                  ..color = Colors.black,
                              ),
                            ),
                            Text(
                              "Starting Stack: $startingStack",
                              style: TextStyle(
                                fontSize: 22,
                                fontWeight: FontWeight.w900,
                                color: Colors.white,
                              ),
                            ),
                          ],
                        ),
                                                
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Name input
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 0, vertical: 4),
                    child: Container(
                      height: 56,
                      alignment: Alignment.centerLeft,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.10),
                        borderRadius: BorderRadius.circular(18),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.18),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: TextFormField(
                          textAlign: TextAlign.center,
                          controller: _nameController,
                          cursorColor: const Color(0xFF4FC3F7),
                          maxLength: 16,
                          buildCounter: (ctx,
                                  {required currentLength,
                                  required isFocused,
                                  maxLength}) =>
                              null,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 22,
                          ),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                            hintText: 'Your Name',
                            hintStyle: TextStyle(
                              color: Color(0xFF7CA1A7),
                              fontWeight: FontWeight.w900,
                              fontSize: 22,
                            ),
                            counterText: '',
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Name is required';
                            }
                            return null;
                          },
                        ),
                      ),
                    ),
                  ),
                  
                            const SizedBox(height: 16),

                            // Seat selector
                            if (availableSeats.isNotEmpty && _selectedSeatPosition >= 0)
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  const Text(
                                    "Selected Seat:",
                                    style:
                                        TextStyle(color: Colors.white),
                                  ),
                                  _pillDropdown(
                                    value: (_selectedSeatPosition + 1).toString(),
                                    items: availableSeats.map((seat) => (seat + 1).toString()).toList(),
                                    onChanged: (v) =>
                                        setState(() => _selectedSeatPosition = int.parse(v!) - 1)),
                                  Text((_selectedSeatPosition + 1).toString())
                                ],
                              ),
                            const SizedBox(height: 16),

                            // Join button
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton(
                                style: ElevatedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(
                                          vertical: 14),
                                  backgroundColor:
                                      const Color.fromARGB(
                                          255, 4, 159, 206),
                                  shape: RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(6),
                                  ),
                                ),
                                onPressed: _isLoading
                                    ? null
                                    : _handleJoinTable,
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 20,
                                        height: 20,
                                        child:
                                            CircularProgressIndicator(
                                          valueColor:
                                              AlwaysStoppedAnimation<
                                                  Color>(Colors.white),
                                          strokeWidth: 2,
                                        ),
                                      )
                                    : const Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment
                                                .center,
                                        children: [
                                          Text(
                                            "Take Seat",
                                            style: TextStyle(
                                              fontSize: 16,
                                              color: Colors.white,
                                            ),
                                          ),
                                          SizedBox(width: 8),
                                          Icon(Icons.start,
                                              color: Colors.white),
                                        ],
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _pillDropdown({
    required String value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Container(
      width: 160,
      height: 48,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color.fromARGB(255, 0, 88, 91),
            Color.fromARGB(255, 2, 34, 37)
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.25),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: Colors.black, width: 2),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: value,
          icon: const Icon(Icons.arrow_drop_down, color: Colors.white),
          dropdownColor: const Color(0xFF08777F),
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w900,
            fontSize: 14,
            shadows: [
              Shadow(blurRadius: 2, color: Colors.black, offset: Offset(1, 1))
            ],
          ),
          onChanged: onChanged,
          items: items
              .map((item) => DropdownMenuItem<String>(
                    value: item,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 4),
                      child: Stack(
                        children: [
                          Text(
                            item,
                            style: TextStyle(
                              fontWeight: FontWeight.w900,
                              fontSize: 20,
                              foreground: Paint()
                                ..style = PaintingStyle.stroke
                                ..strokeWidth = 3
                                ..color = Colors.black,
                            ),
                          ),
                          Text(
                            item,
                            style: const TextStyle(
                              fontWeight: FontWeight.w900,
                              fontSize: 20,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ))
              .toList(),
        ),
      ),
    );
  }

}
