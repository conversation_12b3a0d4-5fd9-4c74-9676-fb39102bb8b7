import 'package:flutter/material.dart';

class PlayingCardPainter extends CustomPainter {
  final String rank;
  final String suit;
  final Color suitColor;
  final double suitSizeMultiplier;

  PlayingCardPainter({
    required this.rank,
    required this.suit,
    required this.suitColor,
    this.suitSizeMultiplier = 0.6,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint borderPaint = Paint()
      ..color = Colors.black.withAlpha(25)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0;

    // Scale border radius based on card size
    final double borderRadius = (size.width * 0.15).clamp(4.0, 12.0);
    final cardRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(borderRadius),
    );

    // Draw card background
    canvas.drawRRect(cardRect, Paint()..color = Colors.white);
    canvas.drawRRect(cardRect, borderPaint);

    // Scale font sizes based on card dimensions
    final double rankFontSize = (size.width * suitSizeMultiplier).clamp(10.0, 24.0);
    final double centerSuitFontSize = (size.width * suitSizeMultiplier).clamp(20.0, 80.0);

    // Scale padding based on card size
    final double padding = (size.width * 0.1).clamp(4.0, 8.0);

    final textStyle = TextStyle(color: suitColor, fontSize: rankFontSize , fontWeight: FontWeight.w900,    fontFamily: 'Arial Black', );

    TextPainter createTextPainter(String text, TextStyle style) {
      final tp = TextPainter(
        text: TextSpan(text: text, style: style),
        textDirection: TextDirection.ltr,
      );
      tp.layout();
      return tp;
    }

    // Draw top-left rank and suit
    final rankPainter = createTextPainter(rank, textStyle);
    rankPainter.paint(canvas, Offset(padding, padding));

    // Draw bottom-right (rotated) rank and suit
    canvas.save();
    canvas.translate(size.width - padding, size.height - padding);
    //canvas.rotate(3.1416); // 180 degrees
    rankPainter.paint(canvas, Offset(-rankPainter.width, -rankPainter.height));
    canvas.restore();

    // Draw center suit symbol
    final pipPainter = createTextPainter(suit, TextStyle(color: suitColor, fontSize: centerSuitFontSize));
    final centerX = (size.width / 2) - (pipPainter.width / 2);
    final centerY = (size.height / 2) - (pipPainter.height / 2);
    pipPainter.paint(canvas, Offset(centerX, centerY));
  }

  
void drawRotatedPip(Canvas canvas, Offset center, TextPainter pipPainter) {
  canvas.save();

  // Move to the center of the pip
  canvas.translate(center.dx, center.dy);

  // Rotate 180 degrees (in radians)
  canvas.rotate(3.1416); // π radians = 180°

  // Draw pip centered, since we've already translated
  pipPainter.paint(canvas, Offset(-pipPainter.width / 2, -pipPainter.height / 2));

  canvas.restore();
}
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class PlayingCardWidget extends StatelessWidget {
  final String rank;
  final String suit;
  final double width;
  final double height;
  final double suitSizeMultiplier;

  const PlayingCardWidget({
    required this.rank,
    required this.suit,
    this.width = 60,
    this.height = 90,
    this.suitSizeMultiplier = 0.6,
    super.key
  });

  @override
  Widget build(BuildContext context) {
    final Color suitColor = (suit == '♥' || suit == '♦') ? Colors.red : Colors.black;

    return CustomPaint(
      size: Size(width, height),
      painter: PlayingCardPainter(
        rank: rank,
        suit: suit,
        suitColor: suitColor,
        suitSizeMultiplier: suitSizeMultiplier,
      ),
    );
  }

  // Factory constructors for different card contexts

  /// Creates a card widget sized for player hands (default size)
  factory PlayingCardWidget.hand({
    required String rank,
    required String suit,
    Key? key,
  }) {
    return PlayingCardWidget(
      rank: rank,
      suit: suit,
      width: 80,
      height: 120,
      key: key,
    );
  }

  /// Creates a card widget sized for community cards on the table
  factory PlayingCardWidget.community({
    required String rank,
    required String suit,
    bool isMobile = false,
    double? width,
    double? height,
    Key? key,
  }) {
    return PlayingCardWidget(
      rank: rank,
      suit: suit,
      width: width ?? (isMobile ? 30.0 : 60.0),
      height: height ?? (isMobile ? 45.0 : 90.0),
      suitSizeMultiplier: 0.6, // Larger suit for community cards
      key: key,
    );
  }

  /// Creates a card widget sized for burned cards
  factory PlayingCardWidget.burned({
    required String rank,
    required String suit,
    required double screenWidth,
    Key? key,
  }) {
    return PlayingCardWidget(
      rank: rank,
      suit: suit,
      width: 60 ,
      height: 90 ,
      key: key,
    );
  }

  /// Creates a small card widget for compact displays
  factory PlayingCardWidget.small({
    required String rank,
    required String suit,
    Key? key,
  }) {
    return PlayingCardWidget(
      rank: rank,
      suit: suit,
      width: 60,
      height: 90,
      key: key,
    );
  }
}
