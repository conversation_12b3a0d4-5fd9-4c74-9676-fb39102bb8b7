import 'package:flutter/material.dart';

class CustomMenu extends StatelessWidget {
  final VoidCallback? onMenuPressed;
  final VoidCallback? onSettingsPressed;
  final bool showCountdown;
  String  countDown;
  CustomMenu({
    Key? key,
    this.onMenuPressed,
    this.onSettingsPressed, bool this.showCountdown = true,
    this.countDown = '',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15 , vertical: 10),
      height: 80,
      decoration: BoxDecoration(
        color: Colors.transparent,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildMenuButton(),
          if(showCountdown)
          SizedBox(width: 150),
          _buildLogo(),
          if(showCountdown)
          Container(
                  padding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                  constraints: BoxConstraints(minHeight: 40, minWidth: 150),
                  alignment: Alignment.center,
                  child: Stack(
                                    alignment: Alignment.center,
                                    children: [
                                      Text(
                                        countDown,
                                        style: TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w900,
                                          foreground: Paint()
                                            ..style = PaintingStyle.stroke
                                            ..strokeWidth = 4
                                            ..color = Colors.black,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                      Text(
                                        countDown,
                                        style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.w900,
                                          color: Color.fromARGB(255, 206, 134, 26),
                                        ),
                                        textAlign: TextAlign.center,
                                      )
                                    ]),
              ),
          _buildSettingsButton(),
        ],
      ),
    );
  }

  Widget _buildMenuButton() {
    return _buildIconButton(
      onPressed: onMenuPressed,
      imageUrl: Icons.menu,
    );
  }

  Widget _buildLogo() {
    return Image.asset('assets/images/4LeafPoker.png');
  }

  Widget _buildSettingsButton() {
    return _buildIconButton(
      onPressed: onSettingsPressed,
      imageUrl: Icons.settings,
    );
  }

  Widget _buildIconButton({
    required VoidCallback? onPressed,
    required dynamic imageUrl,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFF004447),
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: const Color(0xCC000000),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(10),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Icon( imageUrl, color: Colors.white),
          ),
        ),
      ),
    );
  }
}

