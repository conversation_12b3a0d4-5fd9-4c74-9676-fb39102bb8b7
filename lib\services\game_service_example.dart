import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import '../hive/poker_table.dart';
import 'game_service.dart';

/// Example usage of GameService functions
/// This file demonstrates how to use the GameService to retrieve games from Hive
class GameServiceExample {
  
  /// Example function showing how to retrieve all games from Hive
  static Future<void> demonstrateGetGamesFromHive() async {
    debugPrint('=== GameService Example: Retrieving Games from Hive ===');
    
    try {
      // Check if Hive is ready
      if (!GameService.isHiveReady()) {
        debugPrint('Hive is not ready. Make sure Hive.initFlutter() and box opening is completed.');
        return;
      }
      
      // Get all games from Hive
      final List<Map<String, String>> allGames = GameService.getGamesFromHiveSync();
      debugPrint('Total games found: ${allGames.length}');
      
      if (allGames.isEmpty) {
        debugPrint('No games found in Hive database.');
        debugPrint('You can add sample games using addSampleGamesToHive()');
      } else {
        debugPrint('Games retrieved:');
        for (int i = 0; i < allGames.length; i++) {
          final game = allGames[i];
          debugPrint('Game ${i + 1}:');
          debugPrint('  Type: ${game['type']}');
          debugPrint('  Name: ${game['name']}');
          debugPrint('  Blinds: ${game['blinds']}');
          debugPrint('  Buy-in: ${game['buyIn']}');
          debugPrint('  Players: ${game['players']}');
          debugPrint('  Wait: ${game['wait']}');
          debugPrint('  Hours: ${game['hours']}');
          debugPrint('  Table ID: ${game['tableId']}');
          debugPrint('');
        }
      }
      
      // Demonstrate filtering functions
      final activeGames = GameService.getActiveGamesFromHive();
      debugPrint('Active games (with players): ${activeGames.length}');
      
      final tournamentGames = GameService.getTournamentGamesFromHive();
      debugPrint('Tournament games: ${tournamentGames.length}');
      
      final cashGames = GameService.getCashGamesFromHive();
      debugPrint('Cash games: ${cashGames.length}');
      
    } catch (e) {
      debugPrint('Error in demonstration: $e');
    }
  }
  
  /// Adds sample games to Hive for testing purposes
  static Future<void> addSampleGamesToHive() async {
    debugPrint('=== Adding Sample Games to Hive ===');
    
    try {
      // Sample Tournament
      final tournament = PokerTable(
        tableId: 'tournament_001',
        isTournament: true,
        tournamentStartTime: DateTime.now().subtract(const Duration(hours: 2)),
        prizePool: 10000.0,
        seatCount: 9,
        smallBlind: 25,
        bigBlind: 50,
        totalEntrants: 120,
        remainingPlayers: 45,
        placesPaid: 15,
        seatMap: {
          0: 'player_001',
          1: 'player_002',
          2: null,
          3: 'player_003',
          4: null,
          5: 'player_004',
          6: null,
          7: null,
          8: null,
        },
      );
      
      // Sample Cash Game
      final cashGame = PokerTable(
        tableId: 'cash_001',
        isTournament: false,
        minBuyIn: 100.0,
        maxBuyIn: 500.0,
        seatCount: 6,
        smallBlind: 1,
        bigBlind: 2,
        seatMap: {
          0: 'player_005',
          1: 'player_006',
          2: 'player_007',
          3: null,
          4: null,
          5: null,
        },
      );
      
      // Sample Empty Table
      final emptyTable = PokerTable(
        tableId: 'empty_001',
        isTournament: false,
        minBuyIn: 50.0,
        maxBuyIn: 200.0,
        seatCount: 9,
        smallBlind: 5,
        bigBlind: 10,
        seatMap: {},
      );
      
      // Add games to Hive
      await GameService.addGameToHive(tournament);
      await GameService.addGameToHive(cashGame);
      await GameService.addGameToHive(emptyTable);
      
      debugPrint('Successfully added 3 sample games to Hive:');
      debugPrint('1. Tournament (2 hours running, 4/9 players)');
      debugPrint('2. Cash Game (3/6 players)');
      debugPrint('3. Empty Table (0/9 players)');
      
    } catch (e) {
      debugPrint('Error adding sample games: $e');
    }
  }
  
  /// Clears all games from Hive (for testing purposes)
  static Future<void> clearAllGamesFromHive() async {
    debugPrint('=== Clearing All Games from Hive ===');
    
    try {
      final Box<PokerTable> box = Hive.box<PokerTable>('poker_tables');
      await box.clear();
      debugPrint('All games cleared from Hive database.');
    } catch (e) {
      debugPrint('Error clearing games: $e');
    }
  }
  
  /// Complete demonstration workflow
  static Future<void> runCompleteDemo() async {
    debugPrint('=== Complete GameService Demonstration ===');
    
    // Clear existing data
    await clearAllGamesFromHive();
    
    // Show empty state
    await demonstrateGetGamesFromHive();
    
    // Add sample data
    await addSampleGamesToHive();
    
    // Show populated state
    await demonstrateGetGamesFromHive();
    
    debugPrint('=== Demo Complete ===');
  }
}

/// Widget that demonstrates GameService usage in a UI context
class GameServiceDemoWidget extends StatefulWidget {
  const GameServiceDemoWidget({super.key});
  
  @override
  State<GameServiceDemoWidget> createState() => _GameServiceDemoWidgetState();
}

class _GameServiceDemoWidgetState extends State<GameServiceDemoWidget> {
  List<Map<String, String>> games = [];
  bool isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _loadGames();
  }
  
  void _loadGames() {
    setState(() {
      isLoading = true;
    });
    
    try {
      final loadedGames = GameService.getGamesFromHiveSync();
      setState(() {
        games = loadedGames;
        isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading games: $e');
      setState(() {
        games = [];
        isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GameService Demo'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadGames,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () async {
              await GameServiceExample.addSampleGamesToHive();
              _loadGames();
            },
          ),
          IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () async {
              await GameServiceExample.clearAllGamesFromHive();
              _loadGames();
            },
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : games.isEmpty
              ? const Center(
                  child: Text(
                    'No games found.\nTap + to add sample games.',
                    textAlign: TextAlign.center,
                  ),
                )
              : ListView.builder(
                  itemCount: games.length,
                  itemBuilder: (context, index) {
                    final game = games[index];
                    return Card(
                      margin: const EdgeInsets.all(8),
                      child: ListTile(
                        title: Text(game['name'] ?? 'Unknown Game'),
                        subtitle: Text(
                          'Type: ${game['type']}\n'
                          'Blinds: ${game['blinds']}\n'
                          'Players: ${game['players']}\n'
                          'Buy-in: ${game['buyIn']}',
                        ),
                        trailing: Text(
                          'Hours: ${game['hours']}\n'
                          'Wait: ${game['wait']}',
                          textAlign: TextAlign.right,
                        ),
                        isThreeLine: true,
                      ),
                    );
                  },
                ),
    );
  }
}
