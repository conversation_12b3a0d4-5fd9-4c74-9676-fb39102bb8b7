import 'package:flutter/material.dart';
import 'styles.dart';

class FooterComponent extends StatelessWidget {
  const FooterComponent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
              //  decoration: BoxDecoration(
              //   gradient: const LinearGradient(
              //     colors: [
              //       Color.fromARGB(255, 0, 88, 91),
              //       Color.fromARGB(255, 2, 34, 37)
              //     ],
              //     begin: Alignment.topCenter,
              //     end: Alignment.bottomCenter,
              //   ),
              //   border: Border.all(color: Colors.black, width:  1),
              //   // color: const Color(0xFF06606B),
              //   borderRadius: BorderRadius.circular(12),
              //   boxShadow: [
              //     BoxShadow(
              //       color: Colors.black.withOpacity(0.5),
              //       blurRadius: 32,
              //       offset: const Offset(0, 8),
              //     ),
              //   ],
              // ),     
      padding: const EdgeInsets.fromLTRB(0, 0, 0, 12),
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: ElevatedButton(
          onPressed: () {
            // Scroll to top functionality would be implemented here
          },
          style: footerButtonStyle(),
          child: Container(
               decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color.fromARGB(255, 0, 88, 91),
                    Color.fromARGB(255, 2, 34, 37)
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                border: Border.all(color: Colors.black, width:  1),
                // color: const Color(0xFF06606B),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.5),
                    blurRadius: 32,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),  
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 21),
            child: Text(
              'SCROLL TO TOP',
              textAlign: TextAlign.center,
              style: footerTextStyle(),
            ),
          ),
        ),
      ),
    );
  }
}
