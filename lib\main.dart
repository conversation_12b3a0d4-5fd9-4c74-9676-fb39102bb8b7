import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:four_leaf_poker/game-list/GameListComponent.dart';
import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:four_leaf_poker/hive/hand_record.dart';
import 'package:four_leaf_poker/hive/player_stats.dart';
import 'package:four_leaf_poker/hive/poker_table.dart';
import 'package:four_leaf_poker/hive/street_action.dart';

// Import your page widgets here:
import './landing_page.dart';
import './create_table_page.dart';
import './join_table_page.dart';
import './poker_table_page.dart';
import 'game-list/GameList.dart';

Future<void> main() async {
  // Make sure we can call native code before runApp
  WidgetsFlutterBinding.ensureInitialized();

  // Remove the '#' in your URLs
  setUrlStrategy(PathUrlStrategy());

  // 1) Initialize Hive
  await Hive.initFlutter();

  // 2) Register all your adapters
  Hive.registerAdapter(HandRecordAdapter());
  Hive.registerAdapter(StreetActionAdapter());
  Hive.registerAdapter(PlayerStatsAdapter());
  Hive.registerAdapter(PokerTableAdapter());

  // 3) Open your boxes (e.g., hand_records, player_stats, poker_tables)
  await Hive.openBox<HandRecord>('hand_records');
  await Hive.openBox<PlayerStats>('player_stats');
  await Hive.openBox<PokerTable>('poker_tables');

  // Then run your widget
  runApp(const PokerGameAppRoot());
  
}

class PokerGameAppRoot extends StatefulWidget {
  const PokerGameAppRoot({Key? key}) : super(key: key);

  @override
  State<PokerGameAppRoot> createState() => _PokerGameAppRootState();
}

class _PokerGameAppRootState extends State<PokerGameAppRoot> {
  bool _preloaded = false;

  @override
  void initState() {
    super.initState();
    // Preload suits (or other assets) after first frame
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _preloadSuitGlyphs(context);
      // Example: precacheImage(const AssetImage('assets/images/cardback.png'), context);

      setState(() => _preloaded = true);
    });
  }

  // Example function that draws suit glyphs to force them into the font atlas.
  void _preloadSuitGlyphs(BuildContext ctx) {
    final recorder = PictureRecorder();
    final canvas = Canvas(recorder);

    final textPainter = TextPainter(
      text: const TextSpan(
        text: '♠♥♦♣',
        style: TextStyle(fontSize: 24),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(canvas, Offset.zero);
    recorder.endRecording();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '4LeafPoker',
      debugShowCheckedModeBanner: false,
      // We won't use a routes: {} map since we need dynamic parsing.
      // Instead, we use onGenerateRoute:
      onGenerateRoute: (settings) {
        final uri = Uri.parse(settings.name ?? '/');

        // If path is '/', go to LandingPage
        if (uri.path == '/') {
          return MaterialPageRoute(
            builder: (_) => LandingPage(),
          );
        }

        // If path is '/create', go to CreatePage
        if (uri.path == '/casino') {
          return MaterialPageRoute(
            builder: (_) => const CreateTablePage(),
          );
        }
        // If path is '/create', go to CreatePage
        if (uri.path == '/tournaments') {
          return MaterialPageRoute(
            builder: (_) => const GameListPage(),
          );
        }
        // If path is '/game/:roomId'
        if (uri.pathSegments.length == 2 && uri.pathSegments.first == 'game') {
          final roomId = uri.pathSegments[1];
          return MaterialPageRoute(
            settings: settings, // <- attach it
            builder: (_) => PokerTablePage(roomId: roomId),
          );
        }

        // If path is '/game/:roomId/join'
        if (uri.pathSegments.length == 3 &&
            uri.pathSegments.first == 'game' &&
            uri.pathSegments.last == 'join') {
          final roomId = uri.pathSegments[1];
          return MaterialPageRoute(
            builder: (_) => JoinTablePage(roomId: roomId),
          );
        }

        // If no match, default to LandingPage
        return MaterialPageRoute(
          builder: (_) => LandingPage(),
        );
      },
    );
  }
}
