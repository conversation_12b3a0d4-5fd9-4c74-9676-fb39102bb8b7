# GameService Documentation

The `GameService` class provides a comprehensive interface for retrieving and managing poker games from the Hive database.

## Overview

The `GameService` is a static utility class that handles all interactions with the `poker_tables` Hive box. It provides functions to retrieve, filter, add, update, and remove poker games/tables.

## Main Functions

### Retrieving Games

#### `getGamesFromHive()` - Async
```dart
Future<List<Map<String, String>>> getGamesFromHive()
```
Asynchronously retrieves all poker games from the Hive database and returns them in a UI-friendly format.

#### `getGamesFromHiveSync()` - Synchronous
```dart
List<Map<String, String>> getGamesFromHiveSync()
```
Synchronously retrieves all poker games from the Hive database. This is the main function you'll use in most UI contexts.

**Example Usage:**
```dart
// In a StatefulWidget
void _loadGames() {
  try {
    final games = GameService.getGamesFromHiveSync();
    setState(() {
      this.games = games;
    });
  } catch (e) {
    // Handle error
    debugPrint('Error loading games: $e');
  }
}
```

### Filtering Functions

#### `getActiveGamesFromHive()`
```dart
List<Map<String, String>> getActiveGamesFromHive()
```
Returns only games that have active players (at least one seat occupied).

#### `getTournamentGamesFromHive()`
```dart
List<Map<String, String>> getTournamentGamesFromHive()
```
Returns only tournament games.

#### `getCashGamesFromHive()`
```dart
List<Map<String, String>> getCashGamesFromHive()
```
Returns only cash games (non-tournament tables).

### Management Functions

#### `addGameToHive(PokerTable table)`
```dart
Future<void> addGameToHive(PokerTable table)
```
Adds a new poker table to the Hive database.

#### `updateGameInHive(String tableId, PokerTable updatedTable)`
```dart
Future<void> updateGameInHive(String tableId, PokerTable updatedTable)
```
Updates an existing poker table in the database.

#### `removeGameFromHive(String tableId)`
```dart
Future<void> removeGameFromHive(String tableId)
```
Removes a poker table from the database.

### Utility Functions

#### `getGameCount()`
```dart
int getGameCount()
```
Returns the total number of games in the database.

#### `isHiveReady()`
```dart
bool isHiveReady()
```
Checks if the Hive box is open and ready for operations.

## Data Format

All retrieval functions return games in the following format:

```dart
Map<String, String> {
  'type': 'Texas Hold\'em' | 'Tournament',
  'name': 'Display name for the table',
  'blinds': '$small/$big (e.g., $10/$20)',
  'buyIn': 'Buy-in range or tournament prize pool',
  'players': 'active/total (e.g., 6/9)',
  'wait': 'Wait time (e.g., 0:00)',
  'hours': 'Hours running (e.g., 2.5)',
  'tableId': 'Unique table identifier'
}
```

## Integration with UI Components

### GameLobbyLayout Example
```dart
class _GameLobbyLayoutState extends State<GameLobbyLayout> {
  List<Map<String, String>> games = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadGames();
  }

  void _loadGames() {
    try {
      if (!GameService.isHiveReady()) {
        // Use fallback data
        setState(() {
          games = _getFallbackGames();
          isLoading = false;
        });
        return;
      }

      final gamesFromHive = GameService.getGamesFromHiveSync();
      setState(() {
        games = gamesFromHive.isNotEmpty ? gamesFromHive : _getFallbackGames();
        isLoading = false;
      });
    } catch (e) {
      // Handle error with fallback
      setState(() {
        games = _getFallbackGames();
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? CircularProgressIndicator()
        : GameListComponent(games: games);
  }
}
```

## Error Handling

All functions include proper error handling:
- Retrieval functions return empty lists on error
- Management functions rethrow exceptions for proper error propagation
- Utility functions return safe default values

## Prerequisites

Before using GameService, ensure:
1. Hive is initialized: `await Hive.initFlutter()`
2. PokerTable adapter is registered: `Hive.registerAdapter(PokerTableAdapter())`
3. The poker_tables box is opened: `await Hive.openBox<PokerTable>('poker_tables')`

## Testing

Use the `GameServiceExample` class for testing and demonstration:

```dart
// Run complete demo
await GameServiceExample.runCompleteDemo();

// Add sample data
await GameServiceExample.addSampleGamesToHive();

// Clear all data
await GameServiceExample.clearAllGamesFromHive();
```

## Notes

- The service automatically converts `PokerTable` objects to UI-friendly maps
- All functions are static, no instantiation required
- The service handles both tournament and cash game formats
- Player counts are calculated from the `seatMap` field
- Wait times and hours are calculated based on table state and start times
