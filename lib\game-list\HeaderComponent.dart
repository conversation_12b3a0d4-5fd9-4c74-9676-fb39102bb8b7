import 'package:flutter/material.dart';
import 'header_styles.dart';

class HeaderComponent extends StatelessWidget {
  final double width;
  final double height;

  const HeaderComponent({
    Key? key,
    this.width = 1272,
    this.height = 60,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Color(0xFF004447),
        borderRadius: BorderRadius.circular(24),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left section
          Container(
            child: Stack(alignment: Alignment.center, children: [

              Icon(
                Icons.arrow_back,
                color: Colors.black,
                size: 24,
              ),
              Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 22,
              ),
            ]),
          ),

          // Center section
          Expanded(
            child: Container(
              alignment: Alignment.center,
              child:                                                         Stack(
                                                            alignment: Alignment
                                                                .center,
                                                            children: [
                                                              Text(
                                                                "GAME LOBBY",
                                                                style:
                                                                    TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  foreground:
                                                                      Paint()
                                                                        ..style =
                                                                            PaintingStyle.stroke
                                                                        ..strokeWidth =
                                                                            4
                                                                        ..color =
                                                                            Colors.black,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              ),
                                                              Text(
                                                                "GAME LOBBY",
                                                                style:
                                                                    const TextStyle(
                                                                  fontSize: 14,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w900,
                                                                  color: Colors
                                                                      .white,
                                                                ),
                                                                textAlign:
                                                                    TextAlign
                                                                        .center,
                                                              )
                                                            ]),
            ),
          ),

          // Right section
          Container(
            width: 40,
            height: 40,
          ),
        ],
      ),
    );
  }
}
