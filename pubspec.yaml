name: four_leaf_poker
description: "4LeafPoker - Online Multiplayer Poker Room"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter
  flutter_animate: ^4.5.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  http: ^1.3.0
  responsive_builder: ^0.7.1
  socket_io_client: ^3.0.2
  stacked: ^3.4.4
  stacked_services: ^1.6.0
  universal_html: ^2.2.4
  url_strategy: ^0.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  build_runner: ^2.3.3
  stacked_generator: ^1.6.1

flutter:
  uses-material-design: true

  assets:
    - web/_redirects
    - assets/images/cardback.png
    - assets/images/cardback2.jpg
    - assets/images/club.png
    - assets/images/diamond.png
    - assets/images/heart.jpg
    - assets/images/spade.jpg
    - assets/images/stack-of-chips.png
    - assets/images/chips.png
    - assets/images/bgcard.png
    - assets/images/4LPlogo.png
    - assets/images/bg.png
    - assets/images/table.png
    - assets/images/chips.png
    - assets/images/chair.png
    - assets/images/Player.png
    - assets/images/PlayerFrame.png
    - assets/images/Cards.png
    - assets/images/BlankCards.png
    - assets/images/Villian.png
    - assets/images/left-lady.png
    - assets/images/right-lady.png
    - assets/images/leprachaun.png
    - assets/images/leprachaun2.png
    - assets/images/4LeafPoker.png
    - assets/images/tinycards.png
    - assets/images/Black.png
    - assets/images/Passion.png
    - assets/images/Lucky.png
    - assets/images/Purple.png
    - assets/images/Sky.png
    - assets/images/Card-back.png
    - assets/images/dealer.png
    - assets/images/Player-Position.png
    - assets/images/ActionButton.png
    - assets/images/ActionButton2.png
    - assets/images/CreateGame.png
    - assets/images/CreateGame2.png
    - assets/images/JoinGame.png
    - assets/images/VertCharacters.png
    - assets/images/p1.png
    - assets/images/p2.png
    - assets/images/flag1.png
    - assets/images/register.png
    - assets/images/login.png